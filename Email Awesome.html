<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Williamsville Wellness Email Signature Generator</title>
  <style>
  :root {
    --primary: #004824;
    --primary-light: #06502A;
    --accent: #a8763e;
    --bg-light: #e6eed6;
    --white: #ffffff;
    --text-dark: #333333;
    --text-light: #666666;
    --border-light: #e5e5e5;
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 12px rgba(0,0,0,0.15);
    --radius-sm: 6px;
    --radius-md: 12px;
    --radius-lg: 16px;
  }

  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, var(--bg-light) 0%, #f0f8f0 100%);
    color: var(--text-dark);
    line-height: 1.6;
    min-height: 100vh;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
  }

  .header {
    text-align: center;
    margin-bottom: 3rem;
    padding: 2rem 0;
  }

  .header h1 {
    font-size: 2.5rem;
    color: var(--primary);
    margin-bottom: 0.5rem;
    font-weight: 700;
  }

  .header::after {
    content: '';
    display: block;
    margin: 1rem auto;
    width: 80px;
    height: 3px;
    background-color: var(--accent);
  }

  .header p {
    max-width: 600px;
    margin: 0 auto;
    opacity: 0.9;
  }

  .generator-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    margin-bottom: 3rem;
  }

  .selector-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-light);
    transition: all 0.3s ease;
  }

  .selector-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
  }

  .selector-title {
    font-size: 1.5rem;
    color: var(--primary);
    margin-bottom: 0.5rem;
    font-weight: 600;
  }

  .selector-subtitle {
    color: var(--text-light);
    margin-bottom: 1.5rem;
    font-size: 0.95rem;
  }

  .form-group {
    margin-bottom: 1.5rem;
  }

  .form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-dark);
  }

  .form-select, .form-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid var(--border-light);
    border-radius: var(--radius-sm);
    font-size: 1rem;
    transition: all 0.3s ease;
    background: var(--white);
  }

  .form-select:focus, .form-input:focus {
    outline: none;
    border-color: var(--accent);
    box-shadow: 0 0 0 3px rgba(168, 118, 62, 0.1);
  }

  .signature-preview {
    background: #f9f9f9;
    border: 2px solid #e5e5e5;
    border-radius: var(--radius-md);
    padding: 2rem;
    font-family: Arial, sans-serif;
    font-size: 14px;
    color: #333;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }

  .signature-preview:hover {
    border-color: var(--primary-light);
    background: #f5f8f5;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
  }

  .signature-content {
    display: flex;
    gap: 20px;
    align-items: flex-start;
    width: 100%;
  }

  .signature-photo {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    border: 3px solid var(--accent);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    flex-shrink: 0;
    background-color: var(--bg-light);
  }

  .signature-info {
    flex: 1;
    border-left: 3px solid var(--accent);
    padding-left: 20px;
  }

  .signature-name {
    font-size: 18px;
    font-weight: bold;
    color: var(--primary-light);
    margin-bottom: 4px;
  }

  .signature-title {
    font-size: 14px;
    color: #555;
    margin-bottom: 8px;
    font-style: italic;
  }

  .signature-details {
    font-size: 12px;
    color: #333;
    line-height: 1.6;
    margin-bottom: 10px;
  }

  .signature-details div {
    margin-bottom: 2px;
  }

  .signature-details a {
    color: #333;
    text-decoration: none;
  }

  .signature-social {
    margin: 10px 0;
  }

  .signature-social a {
    display: inline-block;
    margin-right: 8px;
  }

  .signature-social img {
    width: 20px;
    height: 20px;
  }

  .signature-logo {
    margin: 10px 0;
  }

  .signature-logo img {
    max-width: 240px;
    height: auto;
    display: block;
  }

  .signature-confidentiality {
    font-size: 10px;
    color: #666;
    font-style: italic;
    line-height: 1.4;
    margin-top: 15px;
    padding-top: 10px;
    border-top: 1px solid #eee;
  }

  .no-selection {
    color: var(--text-light);
    font-style: italic;
    text-align: center;
  }

  .copy-btn {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
    color: var(--white);
    border: none;
    padding: 1rem 2rem;
    border-radius: var(--radius-sm);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    margin-top: 1rem;
  }

  .copy-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
  }

  .copy-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .copy-btn.copied {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  }

  .manual-entry-form {
    display: none;
    background: #f8f9fa;
    border-radius: var(--radius-md);
    padding: 1.5rem;
    margin-top: 1rem;
    border: 1px solid var(--border-light);
  }

  .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }

  .generate-btn {
    background: var(--accent);
    color: var(--white);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius-sm);
    font-size: 0.95rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 1rem;
  }

  .generate-btn:hover {
    background: #956a37;
    transform: translateY(-1px);
  }

  .warning-box {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: var(--radius-md);
    padding: 1.5rem;
    margin-top: 2rem;
    text-align: center;
  }

  .warning-box h4 {
    color: #856404;
    margin-bottom: 0.5rem;
  }

  .warning-box p {
    color: #856404;
    margin: 0;
  }

  .warning-box a {
    color: var(--primary);
    text-decoration: none;
    font-weight: 600;
  }

  .warning-box a:hover {
    text-decoration: underline;
  }

  @media (max-width: 768px) {
    .generator-section {
      grid-template-columns: 1fr;
      gap: 2rem;
    }
    
    .form-row {
      grid-template-columns: 1fr;
    }
    
    .signature-content {
      flex-direction: column;
      text-align: center;
    }
    
    .signature-info {
      border-left: none;
      border-top: 3px solid var(--accent);
      padding-left: 0;
      padding-top: 20px;
    }
  }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Email Signature Generator</h1>
      <p>Create professional email signatures for Williamsville Wellness team members</p>
    </div>
