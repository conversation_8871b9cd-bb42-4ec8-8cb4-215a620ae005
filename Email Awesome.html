<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Signature Generator - Williamsville Wellness</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
<style>
    :root {
        --primary: #06502A;
        --primary-light: #097D43;
        --accent: #a8763e;
        --gold: #A4743E;
        --bg-light: #f7f9f8;
        --text-dark: #333;
        --text-light: #555;
        --white: #fff;
        --shadow-sm: 0 5px 15px rgba(0,0,0,0.05);
        --shadow-md: 0 10px 30px rgba(0,0,0,0.1);
        --shadow-lg: 0 15px 40px rgba(0,0,0,0.15);
        --radius-sm: 8px;
        --radius-md: 10px;
        --radius-lg: 30px;
    }

    *, *::before, *::after {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
    }

    body {
        font-family: 'Poppins', -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: 1rem;
        line-height: 1.6;
        color: var(--text-dark);
        background-color: var(--white);
    }

    .container {
        width: min(1200px, 95%);
        margin: 0 auto;
        padding: 2rem 0;
    }

    .header {
        text-align: center;
        margin-bottom: 3rem;
        padding: 2rem;
        background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
        color: var(--white);
        border-radius: var(--radius-md);
    }

    .header h1 {
        font-size: clamp(1.8rem, 3vw, 2.5rem);
        font-weight: 700;
        margin-bottom: 1rem;
        color: #fff !important;
        position: relative;
        display: inline-block;
    }

    .header h1::after {
        content: '';
        position: absolute;
        bottom: -8px;
        left: 50%;
        transform: translateX(-50%);
        width: 80px;
        height: 3px;
        background-color: var(--accent);
    }

    .header p {
        max-width: 600px;
        margin: 0 auto;
        opacity: 0.9;
    }

    .generator-section {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 3rem;
        margin-bottom: 3rem;
    }

    .selector-card {
        background: var(--white);
        border: 2px solid #e5e5e5;
        border-radius: var(--radius-md);
        padding: 2rem;
        box-shadow: var(--shadow-sm);
    }

    .selector-title {
        color: var(--primary);
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 1rem;
        text-align: center;
    }

    .selector-subtitle {
        color: var(--text-light);
        font-size: 0.9rem;
        text-align: center;
        margin-bottom: 2rem;
    }

    .staff-selector {
        width: 100%;
        padding: 1rem;
        border: 2px solid #e5e5e5;
        border-radius: var(--radius-sm);
        font-size: 1rem;
        font-family: inherit;
        background: var(--white);
        color: var(--text-dark);
        transition: all 0.3s ease;
    }

    .staff-selector:focus {
        outline: none;
        border-color: var(--accent);
        box-shadow: 0 0 0 3px rgba(168, 118, 62, 0.1);
    }

    .signature-preview {
        background: #f9f9f9;
        border: 2px solid #e5e5e5;
        border-radius: var(--radius-md);
        padding: 2rem;
        font-family: Arial, sans-serif;
        font-size: 14px;
        line-height: 1.4;
        margin-bottom: 1rem;
        min-height: 400px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .signature-preview:hover {
        border-color: var(--primary-light);
        background: #f5f8f5;
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }

    .signature-content {
        display: flex;
        align-items: flex-start;
        gap: 20px;
        width: 100%;
        max-width: 600px;
    }

    .signature-photo {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        border: 3px solid var(--accent);
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        flex-shrink: 0;
        background-color: var(--bg-light);
    }

    .signature-info {
        flex: 1;
        border-left: 3px solid var(--accent);
        padding-left: 20px;
    }

    .signature-name {
        font-size: 18px;
        font-weight: bold;
        color: var(--primary);
        margin-bottom: 4px;
    }

    .signature-title {
        font-size: 14px;
        color: var(--text-light);
        margin-bottom: 8px;
        font-style: italic;
    }

    .signature-details {
        font-size: 12px;
        color: var(--text-dark);
        line-height: 1.6;
    }

    .signature-details div {
        margin-bottom: 3px;
    }

    .signature-details a {
        color: var(--text-dark);
        text-decoration: none;
    }

    .signature-social {
        margin: 10px 0;
    }

    .signature-social a {
        margin-right: 8px;
        text-decoration: none;
    }

    .signature-social img {
        width: 20px;
        height: 20px;
        vertical-align: middle;
    }

    .signature-logo {
        margin: 10px 0;
    }

    .signature-logo img {
        max-width: 200px;
        height: auto;
    }

    .signature-confidentiality {
        font-size: 10px;
        color: #666;
        margin-top: 10px;
        font-style: italic;
        line-height: 1.4;
    }

    .copy-btn {
        background: linear-gradient(135deg, var(--accent) 0%, var(--gold) 100%);
        color: var(--white);
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: var(--radius-sm);
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 1rem;
        width: 100%;
    }

    .copy-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(164, 116, 62, 0.3);
    }

    .copy-btn.copied {
        background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
    }

    .copy-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none;
    }

    .no-selection {
        text-align: center;
        color: var(--text-light);
        font-style: italic;
    }

    .instructions {
        background: var(--bg-light);
        border-radius: var(--radius-md);
        padding: 2rem;
        margin-bottom: 3rem;
    }

    .instructions h3 {
        color: var(--primary);
        margin-bottom: 1rem;
        font-size: 1.3rem;
    }

    .instructions ol {
        margin-left: 1.5rem;
    }

    .instructions li {
        margin-bottom: 0.5rem;
    }

    .warning-box {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: var(--radius-sm);
        padding: 1rem;
        margin: 1rem 0;
        color: #856404;
    }

    .warning-box h4 {
        color: #856404;
        margin-bottom: 0.5rem;
        font-size: 1rem;
    }

    @media (max-width: 768px) {
        .generator-section {
            grid-template-columns: 1fr;
        }

        .container {
            padding: 1rem 0;
        }

        .signature-content {
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .signature-info {
            border-left: none;
            border-top: 3px solid var(--accent);
            padding-left: 0;
            padding-top: 15px;
        }
    }
</style>

<div class="container">
    <div class="header">
        <h1>Email Signature Generator</h1>
        <p>Select your name from the dropdown to generate your professional email signature with photo, contact details, and company branding.</p>
    </div>

    <div class="generator-section">
        <!-- Staff Selector -->
        <div class="selector-card">
            <h3 class="selector-title">Select Your Name</h3>
            <p class="selector-subtitle">Choose your name from the list or select "New Staff" for manual entry</p>

            <select class="staff-selector" id="staffSelector">
                <option value="">-- Select Your Name --</option>
                <option value="NEW_STAFF">➕ New Staff (Manual Entry)</option>
            </select>

            <div id="manualEntryForm" style="display: none; margin-top: 2rem; padding: 2rem; background: #f9f9f9; border-radius: var(--radius-sm);">
                <h4 style="color: var(--primary); margin-bottom: 1rem;">Enter Your Information</h4>

                <div style="margin-bottom: 1rem;">
                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Full Name:</label>
                    <input type="text" id="manualName" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;" placeholder="e.g., John Smith, LPC">
                </div>

                <div style="margin-bottom: 1rem;">
                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Job Title:</label>
                    <input type="text" id="manualTitle" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;" placeholder="e.g., Clinical Director">
                </div>

                <div style="margin-bottom: 1rem;">
                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Pronouns:</label>
                    <select id="manualPronouns" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
                        <option value="she/her/hers">she/her/hers</option>
                        <option value="he/him/his">he/him/his</option>
                        <option value="they/them/theirs">they/them/theirs</option>
                    </select>
                </div>

                <div style="margin-bottom: 1rem;">
                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Extension:</label>
                    <input type="text" id="manualExtension" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;" placeholder="e.g., 123">
                </div>

                <div style="margin-bottom: 1rem;">
                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Email:</label>
                    <input type="email" id="manualEmail" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;" placeholder="e.g., <EMAIL>">
                </div>

                <div style="margin-bottom: 1rem;">
                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Staff Type:</label>
                    <select id="manualType" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
                        <option value="clinical">Clinical (includes confidentiality notice)</option>
                        <option value="non-clinical">Non-Clinical</option>
                    </select>
                </div>

                <div style="margin-bottom: 1rem;">
                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Photo URL:</label>
                    <input type="url" id="manualPhoto" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;" placeholder="https://example.com/photo.jpg">
                    <small style="color: var(--text-light);">Upload your photo to your website and paste the direct link here. <a href="mailto:<EMAIL>,<EMAIL>,<EMAIL>?subject=Photo Upload Request&body=Hi team,%0D%0A%0D%0AI need help uploading my photo for my email signature. Please let me know how to proceed.%0D%0A%0D%0AThank you!" style="color: var(--accent);">Don't have a photo link? Contact the team.</a></small>
                </div>

                <button onclick="generateManualSignature()" style="background: var(--accent); color: white; padding: 0.75rem 1.5rem; border: none; border-radius: 4px; cursor: pointer; font-weight: 600;">
                    Generate My Signature
                </button>
            </div>

            <div class="warning-box">
                <h4>⚠️ Important</h4>
                <p><strong>Please double-check all details for accuracy</strong> before copying your signature. Verify your name spelling, title, extension, and email address are correct.</p>
            </div>
        </div>

        <!-- Signature Preview -->
        <div class="selector-card">
            <h3 class="selector-title">Your Email Signature</h3>
            <p class="selector-subtitle">Preview and copy your generated signature</p>

            <div class="signature-preview" id="signaturePreview">
                <div class="no-selection">
                    Select your name to see your signature preview
                </div>
            </div>

            <button class="copy-btn" id="copyBtn" onclick="copySignature()" disabled>
                📋 Copy Signature
            </button>
        </div>
    </div>

<script>
  // Staff data
  const staffData = {
    // Clinical Staff
    "Kelly Dewey": {
      name: "Kelly Dewey, LPC",
      title: "Clinical Director",
      pronouns: "she/her/hers",
      extension: "101",
      email: "<EMAIL>",
      photo: "https://b4030432.smushcdn.com/4030432/wp-content/uploads/2025/06/Staff2-300x300-1.png",
      type: "clinical"
    },
    "Alina Massey": {
      name: "Alina Massey",
      title: "Master's Level Intern",
      pronouns: "she/her/hers",
      extension: "573",
      email: "<EMAIL>",
      photo: "https://b4030432.smushcdn.com/4030432/wp-content/uploads/2025/07/Alina-Massey.png",
      type: "clinical"
    },
    "Arianna Losada": {
      name: "Arianna Losada, BA, QMHP-A",
      title: "Clinician",
      pronouns: "she/her/hers",
      extension: "125",
      email: "<EMAIL>",
      photo: "https://b4030432.smushcdn.com/4030432/wp-content/uploads/2025/06/Staff6-300x300-1.png",
      type: "clinical"
    },
    "Claire Peterson": {
      name: "Claire Peterson, QMHP-T",
      title: "Master's Level Intern",
      pronouns: "she/her/hers",
      extension: "572",
      email: "<EMAIL>",
      photo: "https://b4030432.smushcdn.com/4030432/wp-content/uploads/2025/06/Staff9-300x300-1.png",
      type: "clinical"
    },
    "Emma Grehan": {
      name: "Emma Grehan",
      title: "Master's Level Intern",
      pronouns: "she/her/hers",
      extension: "575",
      email: "<EMAIL>",
      photo: "https://b4030432.smushcdn.com/4030432/wp-content/uploads/2025/06/Emma-Grehan-1.png",
      type: "clinical"
    },
    "Emma Land": {
      name: "Emma Land, LPC",
      title: "Clinician",
      pronouns: "she/her/hers",
      extension: "150",
      email: "<EMAIL>",
      photo: "https://b4030432.smushcdn.com/4030432/wp-content/uploads/2025/06/Staff5-300x300-1.png",
      type: "clinical"
    },
    "Hayley Bray": {
      name: "Hayley Bray",
      title: "Master's Level Intern",
      pronouns: "she/her/hers",
      extension: "574",
      email: "<EMAIL>",
      photo: "https://b4030432.smushcdn.com/4030432/wp-content/uploads/2025/06/Staff13-300x300-1.png",
      type: "clinical"
    },
    "Kara Lee": {
      name: "Kara Lee, LPC, CSAC, NCC, ICGC-I",
      title: "Director of U.R.",
      pronouns: "she/her/hers",
      extension: "576",
      email: "<EMAIL>",
      photo: "https://b4030432.smushcdn.com/4030432/wp-content/uploads/2021/08/kara.png",
      type: "clinical"
    },
    "Lindsey Swietnicki": {
      name: "Lindsey Swietnicki, Resident in Counseling",
      title: "Clinician",
      pronouns: "she/her/hers",
      extension: "201",
      email: "<EMAIL>",
      photo: "https://b4030432.smushcdn.com/4030432/wp-content/uploads/2025/06/Staff10-300x300-1.png",
      type: "clinical"
    },
    "Melody Scott": {
      name: "Melody Scott, LPC",
      title: "Clinician",
      pronouns: "she/her/hers",
      extension: "220",
      email: "<EMAIL>",
      photo: "https://b4030432.smushcdn.com/4030432/wp-content/uploads/2025/06/Staff8-300x300-1.png",
      type: "clinical"
    },
    "Bode Akinbobola": {
      name: "Bode Akinbobola, PhD",
      title: "Clinician",
      pronouns: "he/him/his",
      extension: "108",
      email: "<EMAIL>",
      photo: "https://b4030432.smushcdn.com/4030432/wp-content/uploads/2025/06/Staff7-300x300-1.png",
      type: "clinical"
    },
    "Stephen Smith": {
      name: "Stephen Smith, LPC",
      title: "Clinician",
      pronouns: "he/him/his",
      extension: "126",
      email: "<EMAIL>",
      photo: "https://b4030432.smushcdn.com/4030432/wp-content/uploads/2025/06/Staff4-300x300-1.png",
      type: "clinical"
    },
    "William Hoffman": {
      name: "William \"Billy\" Hoffman, C-PRS, CSAC-A, ICGC-II",
      title: "Peer Recovery Specialist",
      pronouns: "he/him/his",
      extension: "106",
      email: "<EMAIL>",
      photo: "https://b4030432.smushcdn.com/4030432/wp-content/uploads/2025/06/Staff18-300x300-1.png",
      type: "clinical"
    },
    "Kenneth Jones": {
      name: "Kenneth Jones, C-PRS, CSAC-A",
      title: "Peer Recovery Specialist",
      pronouns: "he/him/his",
      extension: "208",
      email: "<EMAIL>",
      photo: "https://b4030432.smushcdn.com/4030432/wp-content/uploads/2025/06/Staff11-300x300-1.png",
      type: "clinical"
    },
    "Walter Wrobleski": {
      name: "Walter Wrobleski, C-PRS, CSAC-A",
      title: "Peer Recovery Specialist",
      pronouns: "he/him/his",
      extension: "128",
      email: "<EMAIL>",
      photo: "https://b4030432.smushcdn.com/4030432/wp-content/uploads/2025/06/Staff3-300x300-1.png",
      type: "clinical"
    },
    "Sherry Lalla": {
      name: "Sherry Lalla, LPN",
      title: "Nurse",
      pronouns: "she/her/hers",
      extension: "103",
      email: "<EMAIL>",
      photo: "https://b4030432.smushcdn.com/4030432/wp-content/uploads/2025/07/Sherry-Lalla-2.png",
      type: "clinical"
    },
    // Non-Clinical Staff
    "Jenny Gilmore": {
      name: "Jenny Gilmore",
      title: "Director of Operations",
      pronouns: "she/her/hers",
      extension: "104",
      email: "<EMAIL>",
      photo: "https://b4030432.smushcdn.com/4030432/wp-content/uploads/2025/06/Staff1-300x300-1.png",
      type: "non-clinical"
    },
    "Allison Weatherford": {
      name: "Allison Weatherford",
      title: "Admissions Specialist",
      pronouns: "she/her/hers",
      extension: "123",
      email: "<EMAIL>",
      photo: "https://b4030432.smushcdn.com/4030432/wp-content/uploads/2025/06/Allison-Weatherford.png",
      type: "non-clinical"
    },
    "Kristen Barney": {
      name: "Kristen Barney",
      title: "Admissions Director of Outpatient Services",
      pronouns: "she/her/hers",
      extension: "300",
      email: "<EMAIL>",
      photo: "https://b4030432.smushcdn.com/4030432/wp-content/uploads/2025/06/Kristen-Barney-1.png",
      type: "non-clinical"
    },
    "Lisa Judd": {
      name: "Lisa Judd",
      title: "President, Chief Operating Officer",
      pronouns: "she/her/hers",
      extension: "100",
      email: "<EMAIL>",
      photo: "https://b4030432.smushcdn.com/4030432/wp-content/uploads/2025/07/Lisa-Judd.png",
      type: "non-clinical"
    },
    "Henry Rodriguez": {
      name: "Henry Rodriguez",
      title: "Technology Director",
      pronouns: "he/him/his",
      extension: "121",
      email: "<EMAIL>",
      photo: "https://b4030432.smushcdn.com/4030432/wp-content/uploads/2025/06/Staff16-300x300-1.png",
      type: "non-clinical"
    },
    "Courtney Judd": {
      name: "Courtney Judd",
      title: "Marketing Director",
      pronouns: "she/her/hers",
      extension: "506",
      email: "<EMAIL>",
      photo: "https://b4030432.smushcdn.com/4030432/wp-content/uploads/2025/07/Courtney-Judd.png",
      type: "non-clinical"
    },
    "Robert Cabaniss": {
      name: "Robert \"Bob\" Cabaniss Jr.",
      title: "Founder, Executive Director",
      pronouns: "he/him/his",
      extension: "119",
      email: "<EMAIL>",
      photo: "https://b4030432.smushcdn.com/4030432/wp-content/uploads/2025/07/Bob-Cabaniss.png",
      type: "non-clinical"
    }
  };

  // Populate dropdown
  function populateDropdown() {
    const selector = document.getElementById('staffSelector');
    const sortedNames = Object.keys(staffData).sort();

    sortedNames.forEach(name => {
      const option = document.createElement('option');
      option.value = name;
      option.textContent = name;
      selector.appendChild(option);
    });
  }

  // Generate signature for preview
  function generateSignature(staffName, manualData = null) {
    const staff = manualData || staffData[staffName];
    if (!staff) return '';

    const socialLinks = `
                <div class="signature-social">
                    <a href="https://www.facebook.com/WilliamsvilleWellness/">
                        <img src="https://cdn-icons-png.flaticon.com/32/733/733547.png" alt="Facebook">
                    </a>
                    <a href="https://www.instagram.com/williamsvillewellness/">
                        <img src="https://cdn-icons-png.flaticon.com/32/2111/2111463.png" alt="Instagram">
                    </a>
                    <a href="https://www.tiktok.com/@williamsville_wellness">
                        <img src="https://cdn-icons-png.flaticon.com/32/3046/3046126.png" alt="TikTok">
                    </a>
                    <a href="https://www.youtube.com/@WilliamsvilleTV">
                        <img src="https://cdn-icons-png.flaticon.com/32/1384/1384060.png" alt="YouTube">
                    </a>
                    <a href="https://www.linkedin.com/company/williamsville-wellness/">
                        <img src="https://cdn-icons-png.flaticon.com/32/733/733561.png" alt="LinkedIn">
                    </a>
                </div>`;

    const companyLogo = `
                <div class="signature-logo">
                    <img src="http://williamsvillewellness.com/wp-content/uploads/2024/08/esb.jpg" alt="Williamsville Wellness">
                </div>`;

    const confidentialityNotice = staff.type === 'clinical' ? `
                <div class="signature-confidentiality">
                    CONFIDENTIALITY NOTICE: Electronic mail is not a secure medium. The privacy of messages cannot be guaranteed. This e-mail message, including all attachments, may contain information that is confidential, proprietary, privileged or otherwise protected by law. It is to be viewed only by the intended recipient(s). If you are not the intended recipient, any disclosure, copying, or distribution of the message, or any action or omission taken by you in reliance on it, is prohibited and may be unlawful. If you are not the intended recipient(s), please notify the sender of this information and delete your copy at once.
                </div>` : '';

    return `
                <div class="signature-content">
                    <div class="signature-photo" style="background-image: url('${staff.photo}')"></div>
                    <div class="signature-info">
                        <div class="signature-name">${staff.name}</div>
                        <div class="signature-title">${staff.title}</div>
                        <div class="signature-details">
                            <div>Pronouns: ${staff.pronouns}</div>
                            <div><strong>Williamsville Wellness, LLC</strong></div>
                            <div><a href="https://maps.google.com/?q=10515+Cabaniss+Lane+Hanover+VA">10515 Cabaniss Lane</a></div>
                            <div><a href="https://maps.google.com/?q=Hanover+VA+23069">Hanover, VA 23069</a></div>
                            <div>☎️ <a href="tel:+18045599959">(*************</a> ext. ${staff.extension}</div>
                            <div>✉️ <a href="mailto:${staff.email}">${staff.email}</a></div>
                            <div>🌐 <a href="https://williamsvillewellness.com">https://williamsvillewellness.com</a></div>
                        </div>
                        ${socialLinks}
                        ${confidentialityNotice}
                    </div>
                </div>
                <div style="margin-top: 15px;">
                    ${companyLogo}
                </div>`;
  }

  // Create signature HTML for copying
  function createCopyableSignature(staffName, manualData = null) {
    const staff = manualData || staffData[staffName];
    if (!staff) return '';

    const confidentialityNotice = staff.type === 'clinical' ? `
                <tr>
                    <td colspan="2" style="padding-top: 15px;">
                        <em style="font-size: 10px; color: #666; font-style: italic; line-height: 1.4;">
                        CONFIDENTIALITY NOTICE: Electronic mail is not a secure medium. The privacy of messages cannot be guaranteed. This e-mail message, including all attachments, may contain information that is confidential, proprietary, privileged or otherwise protected by law. It is to be viewed only by the intended recipient(s). If you are not the intended recipient, any disclosure, copying, or distribution of the message, or any action or omission taken by you in reliance on it, is prohibited and may be unlawful. If you are not the intended recipient(s), please notify the sender of this information and delete your copy at once.
                        </em>
                    </td>
                </tr>` : '';

    return `
                <table cellpadding="0" cellspacing="0" border="0" style="font-family: Arial, sans-serif; font-size: 14px; color: #333;">
                    <tr>
                        <td style="width: 100px; padding-right: 20px; vertical-align: top;">
                            <img src="${staff.photo}" alt="${staff.name}" style="width: 100px; height: 100px; border-radius: 50%; border: 3px solid #a8763e; display: block;">
                        </td>
                        <td style="border-left: 3px solid #a8763e; padding-left: 20px; vertical-align: top;">
                            <div style="font-size: 18px; font-weight: bold; color: #06502A; margin-bottom: 4px;">${staff.name}</div>
                            <div style="font-size: 14px; color: #555; margin-bottom: 8px; font-style: italic;">${staff.title}</div>
                            <div style="font-size: 12px; color: #333; line-height: 1.6;">
                                <div>Pronouns: ${staff.pronouns}</div>
                                <div><strong>Williamsville Wellness, LLC</strong></div>
                                <div><a href="https://maps.google.com/?q=10515+Cabaniss+Lane+Hanover+VA">10515 Cabaniss Lane</a></div>
                                <div><a href="https://maps.google.com/?q=Hanover+VA+23069">Hanover, VA 23069</a></div>
                                <div>☎️ <a href="tel:+18045599959">(*************</a> ext. ${staff.extension}</div>
                                <div>✉️ <a href="mailto:${staff.email}">${staff.email}</a></div>
                                <div>🌐 <a href="https://williamsvillewellness.com">https://williamsvillewellness.com</a></div>
                            </div>
                            <div style="margin: 10px 0 0 0;">
                                <a href="https://www.facebook.com/WilliamsvilleWellness/" style="text-decoration: none;"><img src="https://cdn-icons-png.flaticon.com/32/733/733547.png" alt="Facebook" style="width: 20px; height: 20px; margin-right: 8px;"></a><a href="https://www.instagram.com/williamsvillewellness/" style="text-decoration: none;"><img src="https://cdn-icons-png.flaticon.com/32/2111/2111463.png" alt="Instagram" style="width: 20px; height: 20px; margin-right: 8px;"></a><a href="https://www.tiktok.com/@williamsville_wellness" style="text-decoration: none;"><img src="https://cdn-icons-png.flaticon.com/32/3046/3046126.png" alt="TikTok" style="width: 20px; height: 20px; margin-right: 8px;"></a><a href="https://www.youtube.com/@WilliamsvilleTV" style="text-decoration: none;"><img src="https://cdn-icons-png.flaticon.com/32/1384/1384060.png" alt="YouTube" style="width: 20px; height: 20px; margin-right: 8px;"></a><a href="https://www.linkedin.com/company/williamsville-wellness/" style="text-decoration: none;"><img src="https://cdn-icons-png.flaticon.com/32/733/733561.png" alt="LinkedIn" style="width: 20px; height: 20px;"></a>
                            </div>
                        </td>
                    </tr>
                </table>
                <div style="margin: 10px 0 0 0;">
                    <img src="http://williamsvillewellness.com/wp-content/uploads/2024/08/esb.jpg" alt="Williamsville Wellness" style="max-width: 100%; height: auto; display: block;">
                </div>
                ${confidentialityNotice}`;
  }

  // Copy signature function
  function copySignature() {
    const selector = document.getElementById('staffSelector');
    const selectedStaff = selector.value;
    const button = document.getElementById('copyBtn');

    if (!selectedStaff) return;

    let signatureHTML;
    if (selectedStaff === 'NEW_STAFF') {
      const manualData = getManualData();
      if (!manualData) return;
      signatureHTML = createCopyableSignature(null, manualData);
    } else {
      signatureHTML = createCopyableSignature(selectedStaff);
    }

    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = signatureHTML;
    tempDiv.style.position = 'absolute';
    tempDiv.style.left = '-9999px';
    document.body.appendChild(tempDiv);

    try {
      const range = document.createRange();
      range.selectNodeContents(tempDiv);
      const selection = window.getSelection();
      selection.removeAllRanges();
      selection.addRange(range);

      const successful = document.execCommand('copy');

      if (successful) {
        button.textContent = '✅ Copied!';
        button.classList.add('copied');
        setTimeout(() => {
          button.textContent = '📋 Copy Signature';
          button.classList.remove('copied');
        }, 2000);
      } else {
        alert('Copy failed. Please manually select and copy the signature above.');
      }
    } catch (err) {
      alert('Copy failed. Please manually select and copy the signature above.');
    } finally {
      document.body.removeChild(tempDiv);
      window.getSelection().removeAllRanges();
    }
  }

  // Get manual entry data
  function getManualData() {
    const name = document.getElementById('manualName').value.trim();
    const title = document.getElementById('manualTitle').value.trim();
    const pronouns = document.getElementById('manualPronouns').value;
    const extension = document.getElementById('manualExtension').value.trim();
    const email = document.getElementById('manualEmail').value.trim();
    const type = document.getElementById('manualType').value;
    const photo = document.getElementById('manualPhoto').value.trim();

    if (!name || !title || !extension || !email || !photo) {
      alert('Please fill in all required fields');
      return null;
    }

    return { name, title, pronouns, extension, email, type, photo };
  }

  // Generate manual signature
  function generateManualSignature() {
    const manualData = getManualData();
    if (!manualData) return;

    const preview = document.getElementById('signaturePreview');
    const copyBtn = document.getElementById('copyBtn');

    preview.innerHTML = generateSignature(null, manualData);
    copyBtn.disabled = false;
    copyBtn.style.opacity = '1';
  }

  // Handle staff selection
  document.getElementById('staffSelector').addEventListener('change', function() {
    const selectedStaff = this.value;
    const preview = document.getElementById('signaturePreview');
    const copyBtn = document.getElementById('copyBtn');
    const manualForm = document.getElementById('manualEntryForm');

    if (selectedStaff === 'NEW_STAFF') {
      manualForm.style.display = 'block';
      preview.innerHTML = '<div class="no-selection">Fill out the form below to generate your signature</div>';
      copyBtn.disabled = true;
      copyBtn.style.opacity = '0.5';
    } else if (selectedStaff && staffData[selectedStaff]) {
      manualForm.style.display = 'none';
      preview.innerHTML = generateSignature(selectedStaff);
      copyBtn.disabled = false;
      copyBtn.style.opacity = '1';
    } else {
      manualForm.style.display = 'none';
      preview.innerHTML = '<div class="no-selection">Select your name to see your signature preview</div>';
      copyBtn.disabled = true;
      copyBtn.style.opacity = '0.5';
    }
  });

  // Initialize
  document.addEventListener('DOMContentLoaded', function() {
    populateDropdown();
  });
</script>
</body>
</html>
